<p align="center">
  <img src="https://img.shields.io/badge/FastAPI-MCP-green?logo=fastapi" alt="FastAPI MCP" height="28">
</p>

<p align="center">
  <img src="https://user-images.githubusercontent.com/674621/209430382-1e2e1e7e-2e2e-4e7e-8e2e-1e2e1e7e2e2e.png" alt="esign-qa-mcp-service" width="320">
</p>

<h1 align="center">esign-qa-mcp-service</h1>

<p align="center">
  <b>基于 FastAPI + MCP 协议的电子签名自动化测试服务</b><br>
  <sub>证书管理、测试账号、集测提示词、用例自动生成，一站式解决方案</sub>
</p>

---

## ✨ 项目简介

`esign-qa-mcp-service` 是一个专为电子签名自动化测试场景设计的服务，集成了证书管理、测试账号生成、集测提示词管理等自动化能力，支持与 AI 工具（如 lingma）集成，自动生成 HttpRunner 测试用例。

---

## 🚀 功能特性

- <b>MCP 服务自动暴露</b>：通过 fastapi_mcp 自动暴露所有业务工具接口
- <b>证书管理自动化</b>：支持测试账号获取、证书创建、查询、吊销、更新等全流程
- <b>集测提示词管理</b>：支持多种测试用例/接口提示词的获取与搜索
- <b>标准化测试用例生成</b>：内置标准用例和接口格式模板，便于自动化生成
- <b>灵活配置</b>：所有提示词类型和参考文件均通过配置驱动，易于扩展

---

## 🏁 快速开始

<img src="https://img.shields.io/badge/step-1-blue" height="20"> 安装依赖

```bash
pip install -r requirements.txt
```

<img src="https://img.shields.io/badge/step-2-blue" height="20"> 启动服务

```bash
python main.py
```

<img src="https://img.shields.io/badge/step-3-blue" height="20"> 访问服务

- API 文档: http://localhost:8000/docs
- MCP 端点: http://localhost:8000/mcp
- 健康检查: http://localhost:8000/health_check

---

## 📂 主要目录结构

```text
esign-qa-mcp-service/
├── app/
│   ├── core/                  # 配置
│   ├── mcpController/         # 业务路由
│   └── testCasePromptAndFiles # 提示词与用例模板
├── mcpService/                # 业务工具与提示词服务实现
├── main.py                    # 启动入口
├── requirements.txt           # 依赖
├── Dockerfile                 # 容器部署
└── README.md
```

---

## 🧩 常用接口说明

| 接口路径           | 说明                 |
|--------------------|----------------------|
| `/获取测试账号`     | 获取测试账号信息     |
| `/创建证书`         | 创建数字证书         |
| `/查询证书详情`     | 查询证书详细信息     |
| `/吊销证书`         | 吊销数字证书         |
| `/更新证书`         | 更新数字证书信息     |
| `/获取提示词类型`   | 获取所有可用提示词类型 |
| `/获取提示词内容`   | 获取指定类型提示词内容 |
| `/搜索提示词`       | 根据关键词搜索提示词 |
| `/获取全套集测提示词` | 获取所有集测提示词内容 |

---

## 🛠️ 扩展与自定义

- 新增提示词类型：在 `app/testCasePromptAndFiles/prompt_config.yml` 配置新类型，并添加对应文件
- 新增业务工具：在 `mcpService/utils.py` 或 `mcpService/promptService.py` 添加函数，并在 `app/mcpController/businessRoutes.py` 注册路由

---

## 🎯 适用场景

- 电子签名自动化测试
- 测试账号与证书流程自动化
- 测试用例与接口定义标准化生成
- AI 工具集成的自动化测试场景

---

## 📄 许可证

MIT License
