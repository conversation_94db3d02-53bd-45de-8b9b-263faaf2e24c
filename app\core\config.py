"""
应用配置设置
"""
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    """应用配置类"""
    
    # 项目基本信息
    PROJECT_NAME: str = "esign-qa-mcp-service"
    PROJECT_DESCRIPTION: str = "电子签名QA自动化测试MCP服务"
    VERSION: str = "1.0.0"

    # 服务配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    API_V1_STR: str = "/mcpController/v1"
    
    # MCP配置
    MCP_NAME: str = "FastMCP API Server"
    MCP_DESCRIPTION: str = "Professional MCP server with separated business logic"
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./test.db"
    
    # 外部API配置
    EXTERNAL_API_BASE_URL: str = "http://sdk.testk8s.tsign.cn"

    # AI模型配置
#     DEEPSEEK_API_KEY: str = "***********************************"
#     DEEPSEEK_BASE_URL: str = "https://api.deepseek.com"
#     DEEPSEEK_MODEL: str = "deepseek-chat"
    # 阿里百炼配置
    DEEPSEEK_API_KEY: str = "***********************************"
    DEEPSEEK_BASE_URL: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    DEEPSEEK_MODEL: str = "qwen-turbo"

    # 日志配置
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"

# 创建全局配置实例
settings = Settings()
